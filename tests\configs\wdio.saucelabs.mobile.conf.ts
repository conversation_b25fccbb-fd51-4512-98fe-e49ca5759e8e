import type { Options } from '@wdio/types';
import { join } from 'path';
import { config as sauceSharedConfig } from './wdio.saucelabs.shared.conf.ts';
import { getStepDefinitionFiles } from '../support/helpers/step-definition-finder.ts';

/**
 * SauceLabs Mobile Configuration
 * Extends the shared SauceLabs config with mobile-specific settings
 * Supports iOS Safari testing with scenario-level session isolation
 */

const build = `WebdriverIO - Cucumber - Demo - ${new Date().getTime()}`;

// Environment-specific configuration with fallback to QA
const _environment = process.env.TEST_ENV || 'QA';

// Environment-specific tags (currently empty but ready for future use)
const environmentTags = _environment.toUpperCase() === 'PROD'
  ? ''
  : '';

// Environment-specific specs - all feature files
const specs = [join(process.cwd(), './tests/features/**/*.feature')];

export const config: WebdriverIO.Config & Options.Testrunner = {
  ...sauceSharedConfig,
  automationProtocol: 'webdriver',
  capabilities: [
    {
      // iPhone 15 Pro Max - Latest real iOS device configuration
      'appium:deviceName': 'iPhone.*',
      'appium:automationName': 'XCUITest',
      'appium:platformVersion': '18.*.*',
      'appium:autoAcceptAlerts': false,
      'appium:newCommandTimeout': 300, // 5 minutes timeout for Appium commands
      browserName: 'Safari',
      platformName: 'iOS',
      'sauce:options': {
        build,
        appiumVersion: 'stable',
        maxDuration: 3600, // 1 hour maximum test duration
        idleTimeout: 300, // 5 minutes idle timeout
        commandTimeout: 300, // 5 minutes command timeout
      },
      // Note: webSocketUrl configuration available for future use
    },
  ],

  specs,

  // Standardized service configuration
  services: [
    [
      'sauce',
      {
        region: 'us-west-1',
      },
    ],
    [
      'gmail',
      {
        credentials: join(
          process.cwd(),
          'tests/resources/google-key/gmailCredentials.json',
        ),
        token: join(process.cwd(), 'tests/resources/google-key/token.json'),
        intervalSec: 10,
        timeoutSec: 60,
      },
    ],
  ],

  // Session isolation configuration for scenario-level sessions
  maxInstances: 1, // Force single instance to ensure session isolation
  maxInstancesPerCapability: 1, // One session per capability at a time
  waitforTimeout: 180000, // 3 minutes wait timeout
  connectionRetryTimeout: 180000, // 3 minutes connection retry timeout
  connectionRetryCount: 3, // Maximum connection retry attempts
  framework: 'cucumber' as const,
  specFileRetries: 0, // Disabled in favor of scenario-level retries
  specFileRetriesDelay: 0,
  specFileRetriesDeferred: false,

  // Force session isolation at scenario level
  injectGlobals: true,
  bail: 0, // Don't bail on first failure to allow scenario isolation

  // TypeScript compilation configuration
  autoCompileOpts: {
    autoCompile: true,
    tsNodeOpts: {
      transpileOnly: true,
      project: './tsconfig.json',
    },
  },

  // Enhanced logging for detailed step output
  logLevel: 'info',

  // Cucumber specific configuration with enhanced reporting
  cucumberOpts: {
    require: getStepDefinitionFiles(),
    backtrace: true, // Enable backtrace for better error details
    requireModule: ['ts-node/register'],
    dryRun: false,
    failFast: false,
    colors: true,
    snippets: true,
    source: true,
    strict: false,
    tags: environmentTags,
    timeout: 1600000, // 26+ minutes timeout for long-running scenarios
    ignoreUndefinedDefinitions: false,
    retry: 1, // Default retry count when no CLI --retry is provided
    retryTagFilter: '', // Future use: retry scenarios tagged with @flaky
    scenarioLevelReporter: true,
    // Prioritize step-by-step output with detailed formatting
    format: [
      'pretty', // Primary formatter for step-by-step console output
      'summary', // Summary at the end
      './tests/support/formatters/EnhancedCucumberFormatter.ts', // Enhanced native Cucumber formatter
      'json:reports/cucumber-report.json',
      'rerun:reports/rerun.txt',
      'html:reports/cucumber-report.html',
      'junit:reports/cucumber-report.xml',
    ],
    // Enable detailed step reporting with async-await syntax
    formatOptions: {
      snippetInterface: 'async-await',
    },
  },
};
