import { Then } from '@wdio/cucumber-framework';
import camelLoginPage from '../../pages/camel/camel-login.page.ts';
import logger from '../../support/utils/logger.util.ts';

/**
 * Camel Login Step Definitions
 * Contains step definitions for login functionality on Camel website
 * Implements consistent error handling and logging patterns
 */
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import Registration from '../../pages/commonteps/registration.page.ts';

/**
 * Step definition for validating the Camel signin page
 * @param filepath - Path to the test data file
 * @param sheetname - Sheet name within the test data file
 * @param scenarioname - Specific scenario name for test data
 */
Then(/^The user Validates camel Signin Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    try {
        await logger.info(`Starting Camel signin page validation for scenario: ${scenarioname}`);

        await camelLoginPage.camelloginPageValidation(filepath, sheetname, scenarioname);
        await logger.info('Camel login page validation completed successfully');

        await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
        await logger.info('Sign-in element validation successful');

        await logger.info('User Validates Camel Signin Page - All validations passed');
    } catch (error) {
        await logger.error('Camel signin page validation failed', {
            error,
            filepath,
            sheetname,
            scenarioname,
        });
        throw error;
    }
});

/**
 * Step definition for successful login to Camel application
 * Validates that the user can login and see the Camel logo
 */
Then(/^The user should be able to login to the camel application successfully$/, async function () {
    try {
        await logger.info('Starting Camel application login process');

        await camelLoginPage.camelloginPage();
        await logger.info('Login page interaction completed');

        await expect(camelLoginPageObject.lbllogo_camel).toBeDisplayed();
        await logger.info('Camel logo validation successful');

        await logger.info('User logged in successfully to Camel application');
    } catch (error) {
        await logger.error('Camel application login failed', { error });
        throw error;
    }
});

/**
 * Step definition for validating successful logout from Camel brand
 * Confirms user is redirected to signin page after logout
 */
Then(/^The user validates that successfully logged out of camel brand$/, async function () {
    try {
        await logger.info('Starting Camel logout validation process');

        await camelLoginPage.camellogoutsucessfully();
        await logger.info('Logout process completed');

        await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
        await logger.info('Sign-in page validation successful');

        await logger.info('User successfully logged out and is on signin page');
    } catch (error) {
        await logger.error('Camel logout validation failed', { error });
        throw error;
    }
});

Then(/^The user should be able to login to the nas application successfully$/, async function () {
    await camelLoginPage.nasloginPage();
    await expect(camelLoginPageObject.lbllogo_nas).toBeDisplayed();
    logger.info('User logsin Successfully');

});

Then(/^The user validates that successfully logged out of nas brand$/, async function () {
    await Registration.successfullLogoutfromSensasite();
    await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info('User is on Signin Page');
});





