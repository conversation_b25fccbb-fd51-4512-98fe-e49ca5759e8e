/**
 * Camel Homepage Page Object
 * Contains selectors and methods for interacting with the Camel homepage
 * Implements consistent naming conventions and reduces code duplication
 */
class CamelHomePageObject {
    // Navigation elements
    get lnkproducts_camel() { return $('a[title="Products"]'); }
    get lnkstoreLocator_camel() { return $('a[title="Store Locator"]'); }
    get lnkcamelrewards_camel() { return $('.rewardstatus'); }

    // Warning and banner elements
    get lblwarningHeadline_camel() { return $('.cmp-doj-warning__headline'); }
    get lnkthesestatements_camel() { return $('.cmp-doj-warning__headline a'); }

    // Welcome section elements
    get lblwelcometocamel_camel() { return $('//div[@id="welcome"]//div[@class="cmp-section__container"]'); }
    get lblwelcometocameldesc_camel() { return $$('.cmp-lplus-enrollment__content')[1]; }
    get lblwelcometocameldesc_prod_camel() { return $('(//div[@class="cmp-lplus-enrollment__content"])[2]'); }

    // How it works section
    get lnkhowitworks_camel() { return $('(//a[@title="How it works"])'); }
    get hdrhowitworks_camel() { return $('(//*[normalize-space()="HOW CAMEL POINTS WORK"])[2]'); }

    // Hump section elements
    get lblhumptitle_camel() { return $('#shadow'); }
    get btncheckitout_camel() { return $('a[aria-label="Check It Out"]'); }
    get imghump_camel() { return $('//*[@alt="the hump"]'); }

    // Product section elements
    get lblfindyourfavourites_camel() { return $('//*[text()="Find Your Favorites"]'); }
    get hdrfindyourfavourites_camel() { return $('//h2[contains(text(),"FIND YOUR")]'); }
    get lblyourtast_camel() { return $('//*[text()="Your Taste, Your Style"]'); }
    get btnexplore_camel() { return $('//*[@aria-label="Explore Now"]'); }
    get imgproduct_camel() { return $$('.cmp-image__image')[11]; }
    get imgdaretodiscover_camel() { return $$('.cmp-sms-signup img')[1]; }

    // UI control elements
    get lblhambergercloseicon_camel() { return $('//div[contains(@class,"mobile-nav-icon open")]'); }
    get lblclosefebanner_camel() { return $('//div[@class="cmp-doj-warning__close icon-close"]'); }
    get lblproductpageheader_camel() { return $('(//span[@class="cmp-hero-banner__headline1"])[3]'); }
    get btnhamburgerMenu_camel() { return $('div[aria-label="hamburger menu"]'); }

    /**
     * Get modal content element by index and type
     * Replaces 30+ duplicate selectors with a parameterized approach
     * @param index - Zero-based index of the element
     * @param type - Element type ('p' for paragraph, 'ul' for unordered list)
     * @returns WebdriverIO element
     */
    getModalContentElement(index: number, type: 'p' | 'ul' = 'p'): WebdriverIO.Element {
        return $$(`cmp-doj-warning__modal-content ${type}`)[index];
    }

    /**
     * Get all modal content paragraphs
     * @returns Array of paragraph elements
     */
    get modalContentParagraphs() {
        return $$('.cmp-doj-warning__modal-content p');
    }

    /**
     * Get all modal content lists
     * @returns Array of list elements
     */
    get modalContentLists() {
        return $$('.cmp-doj-warning__modal-content ul');
    }

    /**
     * Get specific modal content paragraph by index
     * @param index - Zero-based index
     * @returns Paragraph element at specified index
     */
    getModalParagraph(index: number): WebdriverIO.Element {
        return this.modalContentParagraphs[index];
    }

    /**
     * Get specific modal content list by index
     * @param index - Zero-based index
     * @returns List element at specified index
     */
    getModalList(index: number): WebdriverIO.Element {
        return this.modalContentLists[index];
    }


}
export default new CamelHomePageObject();