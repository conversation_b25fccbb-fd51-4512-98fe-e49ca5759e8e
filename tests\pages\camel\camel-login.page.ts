import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import RegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaAccountPage from '../commonteps/account.page.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';

/**
 * Camel Login Page Class
 * Handles login functionality and validation for the Camel website
 * Implements comprehensive error handling and logging
 */
class CamelLoginPage {

  /**
   * Validates the Camel login page elements against test data
   * @param filename - Test data file name
   * @param sheetname - Sheet name within the test data
   * @param scenarioname - Scenario name for data lookup
   */
  async camelloginPageValidation(filename: string, sheetname: string, scenarioname: string): Promise<void> {
    try {
      await logger.info(`Starting Camel login page validation for scenario: ${scenarioname}`);

      const SHEET_NAME = sheetname;
      const jsonFilePath = path.join(process.cwd(), 'data', filename);
      const testData = new JsonTestDataHandler(jsonFilePath);
      const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);

      await logger.info(`User Data for ${scenarioname}: ${JSON.stringify(userData1)}`);

      // Extract test data values
      const testDataValues = {
        signin: testData.getCellValue(SHEET_NAME, scenarioname, 'lblsignin'),
        alreadyhave: testData.getCellValue(SHEET_NAME, scenarioname, 'lblhaveanaccount'),
        forgot: testData.getCellValue(SHEET_NAME, scenarioname, 'lblforgot'),
        loginemail: testData.getCellValue(SHEET_NAME, scenarioname, 'lblloginemail'),
        loginremember: testData.getCellValue(SHEET_NAME, scenarioname, 'lblrememberme'),
        loginpassword: testData.getCellValue(SHEET_NAME, scenarioname, 'lblpassword'),
        lblregister: testData.getCellValue(SHEET_NAME, scenarioname, 'lblregister'),
        lblnewaccount: testData.getCellValue(SHEET_NAME, scenarioname, 'lblnewaccount'),
        joinemail: testData.getCellValue(SHEET_NAME, scenarioname, 'lbljoinemail'),
        legalnotice: testData.getCellValue(SHEET_NAME, scenarioname, 'lbllegalNotice'),
        faq: testData.getCellValue(SHEET_NAME, scenarioname, 'lblfaq'),
        siterequirement: testData.getCellValue(SHEET_NAME, scenarioname, 'lblsiterequirement'),
        privacypolicy: testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacypolicy'),
        sustainability: testData.getCellValue(SHEET_NAME, scenarioname, 'lblsustainability'),
        tobacco: testData.getCellValue(SHEET_NAME, scenarioname, 'lbltobbacco'),
        camelpoints: testData.getCellValue(SHEET_NAME, scenarioname, 'lblcamelpoints'),
        contactus: testData.getCellValue(SHEET_NAME, scenarioname, 'lblcontactus'),
        termsofuse: testData.getCellValue(SHEET_NAME, scenarioname, 'lbltermsuse'),
        textmessaging: testData.getCellValue(SHEET_NAME, scenarioname, 'lbltextmessage'),
        copyright: testData.getCellValue(SHEET_NAME, scenarioname, 'lblcopyright'),
      };
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblsignIn_camel, signin);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblalreadyhaveaccount_camel, alreadyhave);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblforgot_camel, forgot);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblloginemail_camel, loginemail);
      await elementActions.assertion(camelLoginPageObject.txtusername_camel);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblloginrememberme_camel, loginremember);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblloginpassword_camel, loginpassword);
      await elementActions.assertion(camelLoginPageObject.txtpassword_camel);
      await elementActions.assertion(camelLoginPageObject.btnlogin_camel);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblregister_camel, lblregister);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblregisteraccount_camel, lblnewaccount);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lbljoinemail_camel, joinemail);
      await elementActions.assertion(camelLoginPageObject.txtregemail_camel);
      await elementActions.assertion(camelLoginPageObject.txtjoinnow_camel);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lbllegalnotice_camel, legalnotice);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkfaq_camel, faq);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnksiteRequirement_camel, siterequirement);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkprivacyPolicy_camel, privacypolicy);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkcontactUs_camel, contactus);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnktermsofUse_camel, termsofuse);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnktextMessaging_camel, textmessaging);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnktobacco_camel, tobacco);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnksustainability_camel, sustainability);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lnkcamelpoits_camel, camelpoints);
      await sensaAccountPage.mssgcomparision(camelLoginPageObject.lblcopyright_camel, copyright);
      console.log('Validated the Content in Login page Successfully');
    } catch (error) {
      logger.error('Failed to Validate the Content in Login page', { error });
      throw error;
    }
  }

  async nasloginPage() {
    try {
      await elementActions.assertion(camelLoginPageObject.lbllogo_nas);
      console.log('User Logged in Successfully');
    } catch (error) {
      logger.error('Unable to Log in ', { error });
      throw error;
    }
  }

  async camelloginPage() {
    try {
      await elementActions.assertion(camelLoginPageObject.lbllogo_camel);
      console.log('User Logged in Successfully');
    } catch (error) {
      logger.error('Unable to Log in ', { error });
      throw error;
    }
  }

  async camellogoutsucessfully() {
    try {
      const signin = await RegistrationPageObject.lblsignIn_sensa;
      const logout = await sensaAccountPageObject.lnklogout_sensa;
      let tries = 0;
      const maxTries = 3;
      if (await browser.getWindowHandle()) {
        while (tries < maxTries) {
          try {
            await browser.execute(() => {
              localStorage.clear();
              sessionStorage.clear();
            });
            await sensaAccountPage.clickonaccountlinkfromheader();
            await elementActions.waitForDisplayed(logout);
            await elementActions.clickusingJavascript(logout);

            await browser.waitUntil(
              async () => await signin.isDisplayed(),
              { timeout: 5000, timeoutMsg: 'Sign-in button did not appear after logout' },
            );
            console.log(`Logout successful on attempt ${tries + 1}`);
            break;
          } catch (attemptError) {
            logger.warn(`Logout attempt ${tries + 1} failed:`, { error: attemptError });
            tries++;
          }
        }
        if (!(await signin.isDisplayed())) {
          throw new Error(`Failed to logout after ${maxTries} attempts`);
        }
      } else {
        console.log('Session already terminated - skipping logout sequence');
      }
      console.log('User Logged out Successfully');
    } catch (error) {
      logger.error('Unable to Log out Successfully ', { error });
      throw error;
    }
  }

}
export default new CamelLoginPage();