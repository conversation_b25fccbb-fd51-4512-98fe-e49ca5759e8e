/**
 * Enhanced Logger Utility for WebDriverIO Test Automation
 * Provides structured logging with SauceLabs session correlation
 * Implements singleton pattern for consistent logging across the framework
 */
import winston from 'winston';

interface LogMetadata {
    [key: string]: unknown;
    error?: Error | unknown;
}

interface SessionInfo {
    sessionId?: string;
    capabilities?: Record<string, unknown>;
    sauceJobId?: string | unknown;
}

class Logger {
    private logger: winston.Logger;
    private static instance: Logger;

    private constructor() {
        this.logger = winston.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.printf(({ timestamp, level, message, sessionId, ...metadata }) => {
                    // Include Saucelabs session ID in logs for easy correlation
                    const sessionInfo = sessionId ? `[Session: ${sessionId}] ` : '';
                    const metadataStr = Object.keys(metadata).length ?
                        ` | ${JSON.stringify(metadata)}` : '';
                    return `${timestamp} ${sessionInfo}[${level}]: ${message}${metadataStr}`;
                }),
            ),
            transports: [
                new winston.transports.Console({
                    format: winston.format.colorize({ all: true }),
                }),
            ],
        });
    }

    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    /**
     * Safely retrieve session information for logging context
     * Handles cases where browser session might not be available
     * @returns Session information object
     */
    private async getSessionInfo(): Promise<SessionInfo> {
        try {
            if (typeof browser === 'undefined') {
                return {};
            }

            const sessionId = browser.sessionId;
            const capabilities = browser.capabilities as Record<string, unknown>;

            // Safely access Sauce Labs job ID
            const sauceOptions = capabilities['sauce:options'] as Record<string, unknown> | undefined;
            const sauceJob = capabilities['sauce:job'] as Record<string, unknown> | undefined;
            const sauceJobId = sauceOptions?.['job-name'] || sauceJob?.id;

            return {
                sessionId,
                capabilities,
                sauceJobId,
            };
        } catch (error) {
            // Return empty object if session info cannot be retrieved
            return {};
        }
    }

    /**
     * Process message and metadata to handle Error objects consistently
     * Extracts error information and formats it for structured logging
     * @param message - Log message or Error object
     * @param metadata - Additional metadata object
     * @returns Processed message and metadata
     */
    private processMessageAndMetadata(
        message: string | Error,
        metadata: LogMetadata = {}
    ): { message: string; metadata: LogMetadata } {
        let processedMessage: string;
        let processedMetadata: LogMetadata = { ...metadata };

        if (message instanceof Error) {
            processedMessage = message.message;
            processedMetadata = {
                ...processedMetadata,
                stackTrace: message.stack,
                errorName: message.name,
                originalError: message,
            };
        } else {
            processedMessage = message;
        }

        // Check if metadata contains an error object
        if (metadata && 'error' in metadata && metadata.error) {
            const error = metadata.error;
            if (error instanceof Error) {
                processedMetadata = {
                    ...processedMetadata,
                    errorMessage: error.message,
                    stackTrace: error.stack,
                    errorName: error.name,
                    originalError: error,
                };
            } else {
                // Handle unknown error types
                processedMetadata = {
                    ...processedMetadata,
                    errorMessage: String(error),
                    errorType: typeof error,
                };
            }
            // Remove the original error from metadata to avoid circular references
            delete processedMetadata.error;
        }

        return { message: processedMessage, metadata: processedMetadata };
    }

    /**
     * Log debug level messages with session context
     * @param message - Debug message or Error object
     * @param metadata - Additional metadata for context
     */
    async debug(message: string | Error, metadata: LogMetadata = {}): Promise<void> {
        try {
            const sessionInfo = await this.getSessionInfo();
            const processed = this.processMessageAndMetadata(message, metadata);
            this.logger.debug(processed.message, { ...sessionInfo, ...processed.metadata });
        } catch (error) {
            // Fallback logging if session info fails
            console.debug('Logger debug fallback:', message, metadata);
        }
    }

    /**
     * Log info level messages with session context
     * @param message - Info message or Error object
     * @param metadata - Additional metadata for context
     */
    async info(message: string | Error, metadata: LogMetadata = {}): Promise<void> {
        try {
            const sessionInfo = await this.getSessionInfo();
            const processed = this.processMessageAndMetadata(message, metadata);
            this.logger.info(processed.message, { ...sessionInfo, ...processed.metadata });
        } catch (error) {
            // Fallback logging if session info fails
            console.info('Logger info fallback:', message, metadata);
        }
    }

    /**
     * Log error level messages with session context and SauceLabs integration
     * @param message - Error message or Error object
     * @param metadata - Additional metadata for context
     */
    async error(message: string | Error, metadata: LogMetadata = {}): Promise<void> {
        try {
            const sessionInfo = await this.getSessionInfo();
            const processed = this.processMessageAndMetadata(message, metadata);

            this.logger.error(processed.message, {
                ...sessionInfo,
                ...processed.metadata,
            });

            // If we have a SauceLabs session, mark the test as failed
            if (typeof browser !== 'undefined' && browser?.execute && sessionInfo.sauceJobId) {
                try {
                    await browser.execute('sauce:job-result=failed');
                } catch (sauceError) {
                    console.warn('Failed to update SauceLabs job status:', sauceError);
                }
            }
        } catch (error) {
            // Fallback logging if session info fails
            console.error('Logger error fallback:', message, metadata);
        }
    }

    /**
     * Log warning level messages with session context
     * @param message - Warning message or Error object
     * @param metadata - Additional metadata for context
     */
    async warn(message: string | Error, metadata: LogMetadata = {}): Promise<void> {
        try {
            const sessionInfo = await this.getSessionInfo();
            const processed = this.processMessageAndMetadata(message, metadata);
            this.logger.warn(processed.message, { ...sessionInfo, ...processed.metadata });
        } catch (error) {
            // Fallback logging if session info fails
            console.warn('Logger warn fallback:', message, metadata);
        }
    }
}

export default Logger.getInstance();
