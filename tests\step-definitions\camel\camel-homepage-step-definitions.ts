import { Then, When } from '@wdio/cucumber-framework';
import camelHomePage from '../../pages/camel/camel-home.page.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import camelHomepagePageObject from '../../page-object/camel/camel-homepage.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';

/**
 * Step definition for validating the Camel homepage with test data from external file
 * @param filepath - Path to the test data file (JSON format)
 * @param sheetname - Sheet name within the test data file
 * @param scenarioname - Specific scenario name to retrieve test data for
 */
Then(/^The user Validates Camel Homepage Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    try {
        await logger.info(`Starting homepage validation for scenario: ${scenarioname}`);

        await camelHomePage.homePageValidation(filepath, sheetname, scenarioname);
        await logger.info('Homepage validation completed successfully');

        await camelHomePage.fedbannerValidation(filepath, sheetname, scenarioname);
        await logger.info('Fed banner validation completed successfully');

        await expect(camelLoginPageObject.lbllogo_camel).toBeDisplayed();
        await logger.info('Camel logo validation completed successfully');

        await logger.info('User Validates Camel Tobacco Rights Page - All validations passed');
    } catch (error) {
        await logger.error('Homepage validation failed', {
            error,
            filepath,
            sheetname,
            scenarioname,
        });
        throw error;
    }
});

/**
 * Step definition for navigating to the Products page
 * Validates that the navigation was successful by checking the page header
 */
When(/^The user clicks on Products$/, async function () {
    try {
        await logger.info('Starting navigation to Products page');

        await camelHomePage.navigatetoProductspage();
        await logger.info('Products page navigation completed');

        await expect(camelHomepagePageObject.lblproductpageheader_camel).toBeDisplayed();
        await logger.info('Products page header validation successful');

        await logger.info('Navigated to Products Page Successfully');
    } catch (error) {
        await logger.error('Failed to navigate to Products page', { error });
        throw error;
    }
});
